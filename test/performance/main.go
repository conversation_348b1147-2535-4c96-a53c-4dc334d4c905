package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"
)

// TestConfig 测试配置
type TestConfig struct {
	ServerURL        string        `json:"server_url"`
	NetworkDelay     time.Duration `json:"network_delay"`     // 模拟网络延迟
	CPULimit         int           `json:"cpu_limit"`         // CPU限制百分比
	MemoryLimit      int           `json:"memory_limit"`      // 内存限制MB
	ConcurrentUsers  int           `json:"concurrent_users"`  // 并发用户数
	TestDuration     time.Duration `json:"test_duration"`     // 测试持续时间
	RequestInterval  time.Duration `json:"request_interval"`  // 请求间隔
}

// TestResult 测试结果
type TestResult struct {
	TestName        string            `json:"test_name"`
	StartTime       time.Time         `json:"start_time"`
	EndTime         time.Time         `json:"end_time"`
	Duration        time.Duration     `json:"duration"`
	TotalRequests   int               `json:"total_requests"`
	SuccessRequests int               `json:"success_requests"`
	FailedRequests  int               `json:"failed_requests"`
	AvgResponseTime time.Duration     `json:"avg_response_time"`
	MinResponseTime time.Duration     `json:"min_response_time"`
	MaxResponseTime time.Duration     `json:"max_response_time"`
	P95ResponseTime time.Duration     `json:"p95_response_time"`
	P99ResponseTime time.Duration     `json:"p99_response_time"`
	Throughput      float64           `json:"throughput"` // 请求/秒
	Errors          map[string]int    `json:"errors"`
	ResponseTimes   []time.Duration   `json:"-"` // 不序列化，用于计算统计
}

// PerformanceTester 性能测试器
type PerformanceTester struct {
	config     TestConfig
	httpClient *http.Client
	results    []TestResult
	mutex      sync.Mutex
}

// NewPerformanceTester 创建性能测试器
func NewPerformanceTester(config TestConfig) *PerformanceTester {
	// 创建带延迟的HTTP客户端
	transport := &http.Transport{
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     30 * time.Second,
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   30 * time.Second,
	}

	return &PerformanceTester{
		config:     config,
		httpClient: client,
		results:    make([]TestResult, 0),
	}
}

// DefaultConfig 默认测试配置（模拟低配VPS）
func DefaultConfig() TestConfig {
	return TestConfig{
		ServerURL:       "http://localhost:9090",
		NetworkDelay:    100 * time.Millisecond, // 100ms延迟
		CPULimit:        25,                      // 限制25% CPU
		MemoryLimit:     512,                     // 限制512MB内存
		ConcurrentUsers: 5,                       // 5个并发用户
		TestDuration:    2 * time.Minute,        // 测试2分钟
		RequestInterval: 2 * time.Second,         // 每2秒一个请求
	}
}

// simulateNetworkDelay 模拟网络延迟
func (pt *PerformanceTester) simulateNetworkDelay() {
	if pt.config.NetworkDelay > 0 {
		time.Sleep(pt.config.NetworkDelay)
	}
}

// makeRequest 发送HTTP请求
func (pt *PerformanceTester) makeRequest(url string) (time.Duration, error) {
	start := time.Now()
	
	// 模拟网络延迟
	pt.simulateNetworkDelay()
	
	resp, err := pt.httpClient.Get(url)
	if err != nil {
		return time.Since(start), err
	}
	defer resp.Body.Close()
	
	// 读取响应体以确保完整请求
	_, err = io.ReadAll(resp.Body)
	if err != nil {
		return time.Since(start), err
	}
	
	if resp.StatusCode >= 400 {
		return time.Since(start), fmt.Errorf("HTTP %d", resp.StatusCode)
	}
	
	return time.Since(start), nil
}

// runPageLoadTest 运行页面加载测试
func (pt *PerformanceTester) runPageLoadTest() TestResult {
	result := TestResult{
		TestName:      "页面加载测试",
		StartTime:     time.Now(),
		Errors:        make(map[string]int),
		ResponseTimes: make([]time.Duration, 0),
	}
	
	// 测试页面列表
	pages := []string{
		"/",
		"/dashboard",
		"/system",
		"/projects",
		"/environment",
	}
	
	log.Printf("🚀 开始页面加载测试...")
	
	for _, page := range pages {
		url := pt.config.ServerURL + page
		log.Printf("测试页面: %s", url)
		
		duration, err := pt.makeRequest(url)
		result.TotalRequests++
		result.ResponseTimes = append(result.ResponseTimes, duration)
		
		if err != nil {
			result.FailedRequests++
			result.Errors[err.Error()]++
			log.Printf("❌ 页面 %s 加载失败: %v (耗时: %v)", page, err, duration)
		} else {
			result.SuccessRequests++
			log.Printf("✅ 页面 %s 加载成功 (耗时: %v)", page, duration)
		}
		
		time.Sleep(500 * time.Millisecond) // 页面间间隔
	}
	
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	pt.calculateStatistics(&result)
	
	return result
}

// runStaticResourceTest 运行静态资源测试
func (pt *PerformanceTester) runStaticResourceTest() TestResult {
	result := TestResult{
		TestName:      "静态资源测试",
		StartTime:     time.Now(),
		Errors:        make(map[string]int),
		ResponseTimes: make([]time.Duration, 0),
	}
	
	// 静态资源列表
	resources := []string{
		"/static/css/output.css",
		"/static/js/htmx.min.js",
		"/static/js/alpine.min.js",
	}
	
	log.Printf("🎨 开始静态资源测试...")
	
	for _, resource := range resources {
		url := pt.config.ServerURL + resource
		log.Printf("测试资源: %s", url)
		
		duration, err := pt.makeRequest(url)
		result.TotalRequests++
		result.ResponseTimes = append(result.ResponseTimes, duration)
		
		if err != nil {
			result.FailedRequests++
			result.Errors[err.Error()]++
			log.Printf("❌ 资源 %s 加载失败: %v (耗时: %v)", resource, err, duration)
		} else {
			result.SuccessRequests++
			log.Printf("✅ 资源 %s 加载成功 (耗时: %v)", resource, duration)
		}
		
		time.Sleep(200 * time.Millisecond) // 资源间间隔
	}
	
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	pt.calculateStatistics(&result)
	
	return result
}

// runAPITest 运行API测试
func (pt *PerformanceTester) runAPITest() TestResult {
	result := TestResult{
		TestName:      "API接口测试",
		StartTime:     time.Now(),
		Errors:        make(map[string]int),
		ResponseTimes: make([]time.Duration, 0),
	}
	
	// API接口列表
	apis := []string{
		"/api/stats",
		"/api/system/overview",
		"/api/system/details",
		"/api/system/processes",
	}
	
	log.Printf("🔌 开始API接口测试...")
	
	for _, api := range apis {
		url := pt.config.ServerURL + api
		log.Printf("测试API: %s", url)
		
		duration, err := pt.makeRequest(url)
		result.TotalRequests++
		result.ResponseTimes = append(result.ResponseTimes, duration)
		
		if err != nil {
			result.FailedRequests++
			result.Errors[err.Error()]++
			log.Printf("❌ API %s 请求失败: %v (耗时: %v)", api, err, duration)
		} else {
			result.SuccessRequests++
			log.Printf("✅ API %s 请求成功 (耗时: %v)", api, duration)
		}
		
		time.Sleep(1 * time.Second) // API间间隔
	}
	
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	pt.calculateStatistics(&result)
	
	return result
}

// calculateStatistics 计算统计数据
func (pt *PerformanceTester) calculateStatistics(result *TestResult) {
	if len(result.ResponseTimes) == 0 {
		return
	}
	
	// 排序响应时间
	times := make([]time.Duration, len(result.ResponseTimes))
	copy(times, result.ResponseTimes)
	
	// 简单排序
	for i := 0; i < len(times); i++ {
		for j := i + 1; j < len(times); j++ {
			if times[i] > times[j] {
				times[i], times[j] = times[j], times[i]
			}
		}
	}
	
	// 计算统计值
	result.MinResponseTime = times[0]
	result.MaxResponseTime = times[len(times)-1]
	
	// 计算平均值
	var total time.Duration
	for _, t := range times {
		total += t
	}
	result.AvgResponseTime = total / time.Duration(len(times))
	
	// 计算P95和P99
	p95Index := int(float64(len(times)) * 0.95)
	p99Index := int(float64(len(times)) * 0.99)
	if p95Index >= len(times) {
		p95Index = len(times) - 1
	}
	if p99Index >= len(times) {
		p99Index = len(times) - 1
	}
	result.P95ResponseTime = times[p95Index]
	result.P99ResponseTime = times[p99Index]
	
	// 计算吞吐量
	if result.Duration > 0 {
		result.Throughput = float64(result.TotalRequests) / result.Duration.Seconds()
	}
}

func main() {
	log.Printf("🧪 DigWis Panel 性能测试工具")
	log.Printf("模拟低配VPS环境性能测试")
	log.Printf("========================================")
	
	config := DefaultConfig()
	
	// 检查服务器是否运行
	log.Printf("🔍 检查服务器状态: %s", config.ServerURL)
	resp, err := http.Get(config.ServerURL)
	if err != nil {
		log.Fatalf("❌ 无法连接到服务器: %v", err)
	}
	resp.Body.Close()
	log.Printf("✅ 服务器连接正常")
	
	tester := NewPerformanceTester(config)
	
	// 运行测试
	log.Printf("📊 开始性能测试...")
	log.Printf("配置: 网络延迟=%v, 并发用户=%d", config.NetworkDelay, config.ConcurrentUsers)
	
	// 页面加载测试
	pageResult := tester.runPageLoadTest()
	tester.results = append(tester.results, pageResult)
	
	// 静态资源测试
	staticResult := tester.runStaticResourceTest()
	tester.results = append(tester.results, staticResult)
	
	// API测试
	apiResult := tester.runAPITest()
	tester.results = append(tester.results, apiResult)
	
	// 生成报告
	generateReport(tester.results)
}

// generateReport 生成测试报告
func generateReport(results []TestResult) {
	log.Printf("\n📋 性能测试报告")
	log.Printf("========================================")
	
	for _, result := range results {
		log.Printf("\n🔸 %s", result.TestName)
		log.Printf("   总请求数: %d", result.TotalRequests)
		log.Printf("   成功请求: %d", result.SuccessRequests)
		log.Printf("   失败请求: %d", result.FailedRequests)
		log.Printf("   成功率: %.2f%%", float64(result.SuccessRequests)/float64(result.TotalRequests)*100)
		log.Printf("   平均响应时间: %v", result.AvgResponseTime)
		log.Printf("   最小响应时间: %v", result.MinResponseTime)
		log.Printf("   最大响应时间: %v", result.MaxResponseTime)
		log.Printf("   P95响应时间: %v", result.P95ResponseTime)
		log.Printf("   P99响应时间: %v", result.P99ResponseTime)
		log.Printf("   吞吐量: %.2f 请求/秒", result.Throughput)
		
		if len(result.Errors) > 0 {
			log.Printf("   错误统计:")
			for err, count := range result.Errors {
				log.Printf("     %s: %d次", err, count)
			}
		}
	}
	
	// 保存JSON报告
	saveJSONReport(results)
}

// saveJSONReport 保存JSON格式的报告
func saveJSONReport(results []TestResult) {
	report := map[string]interface{}{
		"test_time": time.Now().Format(time.RFC3339),
		"results":   results,
	}
	
	data, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		log.Printf("❌ 生成JSON报告失败: %v", err)
		return
	}
	
	filename := fmt.Sprintf("performance_report_%s.json", time.Now().Format("20060102_150405"))
	err = os.WriteFile(filename, data, 0644)
	if err != nil {
		log.Printf("❌ 保存报告失败: %v", err)
		return
	}
	
	log.Printf("\n💾 测试报告已保存: %s", filename)
}
